/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect, useRef } from 'react'
import styles from '../../styles/ModalMassiveLoad.module.css'
import { AiOutlineClose } from 'react-icons/ai'
import Image from 'next/image'
import Button from '../../Button'
import Modal from '../Modal'
import { massiveLoadValidationRules } from '@/utils/validationRules'
import { useTransfersStore } from '@/store/transfers/useTransfersStore'
import MassiveLoadLoading from './MassiveLoadLoading'
import MassiveLoadError from './MassiveLoadError'
import * as XLSX from 'xlsx'
import { BulkTransferData } from '@/types/transfers/types'
import { useAuthStore } from '@/store/auth/useAuthStore'

type MassiveLoadProps = {
  onClose: () => void
  open: boolean
  onReadyForAuthorization: (fileId: string, parsedData: BulkTransferData[]) => Promise<void>
}

const MassiveLoadTransfers = ({ onClose, open, onReadyForAuthorization }: MassiveLoadProps) => {
  const [file, setFile] = useState<File | null>(null)
  const [fileError, setFileError] = useState<string | null>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const [loadingModalOpen, setLoadingModalOpen] = useState(false)
  const [errorModalOpen, setErrorModalOpen] = useState(false)
  const [errors, setErrors] = useState<string[]>([])
  const { user } = useAuthStore()

  const { uploadBulkFile, resetBulkState, bulkTransferErrors } = useTransfersStore()

  useEffect(() => {
    if (!open) {
      setFile(null)
      setFileError(null)
      setErrors([])
      setErrorModalOpen(false)
      resetBulkState()
    }
  }, [open])

  const handleDownloadFormat = () => {
    const link = document.createElement('a')
    link.href = '/formato-de-carga-masiva-transferencias.xlsx'
    link.download = 'Formato_Carga_Masiva_Transferencias.xlsx'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const uploadedFile = event.target.files?.[0] || null
    const error = massiveLoadValidationRules.file(uploadedFile)

    if (error) {
      setFileError(error)
      setFile(null)
    } else {
      setFile(uploadedFile)
      setFileError(null)
    }
  }

  const handleDeleteFile = () => {
    setFile(null)
    setFileError(null)
    if (inputRef.current) {
      inputRef.current.value = ''
    }
  }

  const handleLabelClick = () => {
    if (file) {
      const fileURL = URL.createObjectURL(file)
      const link = document.createElement('a')
      link.href = fileURL
      link.download = file.name
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    } else {
      inputRef.current?.click()
    }
  }

  const validateRowData = (row: Record<string, any>, rowIndex: number): string[] => {
    const errors: string[] = []
    const rowNumber = rowIndex + 2 // +2 porque Excel empieza en 1 y hay header

    // Validar cuenta destino
    const cuentaDestino = row['Cuenta destino (obligatorio)']
    if (!cuentaDestino || String(cuentaDestino).trim() === '') {
      errors.push(`Fila ${rowNumber}: Falta la cuenta destino`)
    } else if (!/^(?:\d{11}|\d{16}|\d{18})$/.test(String(cuentaDestino))) {
      errors.push(
        `Fila ${rowNumber}: Formato de cuenta incorrecto. Debe tener ` +
          `11 dígitos (Convenia), 16 dígitos (tarjeta) o 18 dígitos (CLABE)`
      )
    }

    // Validar importe
    const importeRaw = row['Importe (obligatorio)']
    if (!importeRaw) {
      errors.push(`Fila ${rowNumber}: Falta el importe`)
    } else {
      const amount =
        typeof importeRaw === 'string' ? parseFloat(importeRaw.replace(/[$,]/g, '')) : importeRaw
      if (isNaN(amount) || amount <= 0) {
        errors.push(`Fila ${rowNumber}: El importe debe ser mayor a cero`)
      }
    }

    // Validar nombre del beneficiario
    const beneficiaryName = row['Nombre del beneficiario (obligatorio)']
    if (!beneficiaryName || String(beneficiaryName).trim() === '') {
      errors.push(`Fila ${rowNumber}: Falta el nombre del beneficiario`)
    }

    // Validar concepto
    const concepto = row['Concepto (obligatorio)']
    if (!concepto || String(concepto).trim() === '') {
      errors.push(`Fila ${rowNumber}: Falta el concepto`)
    }

    return errors
  }

  const parseExcelFile = async (file: File): Promise<BulkTransferData[]> => {
    const data: BulkTransferData[] = []
    const validationErrors: string[] = []

    const arrayBuffer = await file.arrayBuffer()
    const workbook = XLSX.read(arrayBuffer, { type: 'array' })
    const sheetName = workbook.SheetNames[0]
    const worksheet = workbook.Sheets[sheetName]
    const json = XLSX.utils.sheet_to_json<Record<string, any>>(worksheet, { raw: true })

    json.forEach((row, i) => {
      // Validar cada fila
      const rowErrors = validateRowData(row, i)
      validationErrors.push(...rowErrors)

      // Solo procesar si no hay errores en esta fila
      if (rowErrors.length === 0) {
        const amount =
          typeof row['Importe (obligatorio)'] === 'string'
            ? parseFloat(row['Importe (obligatorio)'].replace(/[$,]/g, ''))
            : row['Importe (obligatorio)']

        data.push({
          num_clabe: String(row['Cuenta destino (obligatorio)']),
          email: user?.email || '',
          amount: amount || 0,
          description: row['Concepto (obligatorio)'] || '',
          beneficiaryName: row['Nombre del beneficiario (obligatorio)'] || '',
        })
      }
    })

    // Si hay errores de validación, lanzar excepción sin mensaje introductorio (se maneja en el modal)
    if (validationErrors.length > 0) {
      throw new Error(validationErrors.join('\n'))
    }

    return data
  }

  const handleUpload = async () => {
    if (!file) {
      setFileError('Debe seleccionar un archivo antes de cargar.')
      return
    }

    resetBulkState()
    setErrors([])
    setErrorModalOpen(false)

    try {
      setLoadingModalOpen(true)

      // 1. Subir archivo
      const fileId = await uploadBulkFile(file)
      if (!fileId) throw new Error('No se pudo obtener el ID del archivo')

      // 2. Parsear Excel
      const parsedData = await parseExcelFile(file)

      // 3. Enviar a autorización OTP
      await onReadyForAuthorization(fileId, parsedData)
      setLoadingModalOpen(false)
      onClose()
    } catch (err: any) {
      setLoadingModalOpen(false)
      setErrors([err.message])
      setErrorModalOpen(true)
    }
  }

  const handleCloseError = () => {
    setErrorModalOpen(false)
    setErrors([])
    resetBulkState()
  }

  if (loadingModalOpen) {
    return <MassiveLoadLoading open={loadingModalOpen} title="Cargando transferencias masivas" />
  }

  const formatErrorMessage = (error: any): string => {
    // Si el error tiene información de cuenta válida
    if (error.beneficiatyAccount && error.beneficiatyAccount !== 'undefined') {
      return `${error.beneficiatyAccount}: ${error.errorMessage}`
    }

    // Si no hay cuenta o es undefined, mostrar solo el mensaje de error mejorado
    let message = error.errorMessage || 'Error desconocido'

    // Mejorar mensajes específicos según el texto proporcionado
    if (message.includes('Beneficiario no válido') || message.includes('beneficiario')) {
      message = 'Error en nombre de beneficiario.'
    } else if (message.includes('cuenta') || message.includes('account')) {
      message = 'Error en cuenta destino.'
    } else if (message.includes('importe') || message.includes('amount')) {
      message = 'Error en importe.'
    } else if (message.includes('concepto') || message.includes('concept')) {
      message = 'Error en concepto.'
    }

    return message
  }

  const formatCombinedErrors = (errors: string[], transferErrors: any[]): string[] => {
    const formattedErrors = [...errors]

    if (transferErrors && transferErrors.length > 0) {
      // No agregar mensaje introductorio aquí, se maneja en el modal con el title
      formattedErrors.push(...transferErrors.map(formatErrorMessage))
    }

    return formattedErrors
  }

  const combinedErrors = formatCombinedErrors(errors, bulkTransferErrors || [])

  if (errorModalOpen) {
    return (
      <MassiveLoadError
        open={errorModalOpen}
        onClose={handleCloseError}
        errors={combinedErrors}
        title="Se encontraron errores en el archivo. Por favor corrige los siguientes campos:"
      />
    )
  }

  return (
    <Modal open={open}>
      <div className={styles.modalOverlay}>
        <div className={`${styles.modalContainer}`}>
          <Image
            src="/bgespiralmasivemodal.svg"
            alt="Icono"
            width={336}
            height={336}
            style={{
              // backgroundColor: "#000000",
              position: 'absolute',
              top: '-120px',
              left: '-120px',
            }}
          />
          <button className={styles.closeButton} onClick={onClose}>
            <AiOutlineClose />
          </button>
          <div className={styles.header}>
            <h2 className={styles.title}>Transferencia masiva</h2>
            <div className={styles.description}>
              <p>Datos necesarios:</p>
              <p>
                Nombre de la compañía, alias, cuenta destino, importe, nombre del beneficiario, RFC
                (opcional) y concepto
              </p>
            </div>
            <div className={styles.description}>
              <p>Reglas de Validación:</p>
              <ul>
                <li>El nombre de la compañía debe estar dado de alta como cliente.</li>
                <li>El alias debe coincidir con el nombre comercial de la empresa.</li>
                <li>La cuenta destino debe ser una CLABE válida de 18 dígitos.</li>
                <li>
                  El importe debe estar en formato numérico, puede incluir símbolo &#36; y
                  decimales.
                </li>
                <li>El nombre del beneficiario no debe contener caracteres especiales.</li>
                <li>
                  El RFC del beneficiario es opcional, pero si se incluye debe tener un formato
                  válido.
                </li>
                <li>
                  El concepto es obligatorio y debe describir claramente el propósito de la
                  transferencia.
                </li>
              </ul>
            </div>
          </div>

          <div className={styles.actions}>
            <button className={styles.downloadButton} onClick={handleDownloadFormat}>
              Descargar formato
            </button>

            <div>
              <label
                className={!file ? styles.uploadContainer : styles.uploadedContainer}
                onClick={handleLabelClick}
                style={{ cursor: 'pointer' }}
              >
                <div className={styles.uploadIcon}>
                  <Image
                    src="/images.svg"
                    alt="Icono"
                    width={!file ? 24 : 60}
                    height={!file ? 24 : 60}
                  />
                </div>
                <div className={styles.uploadText} title={file?.name}>
                  {file ? file.name : 'Cargar archivo'}
                </div>
              </label>
              <input
                ref={inputRef}
                type="file"
                accept=".csv, .xls, .xlsx"
                style={{ display: 'none' }}
                onChange={handleFileUpload}
              />
              {fileError && <p className={styles.errorText}>{fileError}</p>}
              {file && (
                <button className={styles.deleteButton} onClick={handleDeleteFile}>
                  Eliminar archivo
                </button>
              )}
            </div>
            <Button text="Cargar" fullWidth onClick={handleUpload} />
          </div>
        </div>
      </div>
    </Modal>
  )
}

export default MassiveLoadTransfers
